[package]
name = "deadlock-api-ingest"
version = "0.1.0"
description = "Deadlock API Ingest"
repository = "https://github.com/deadlock-api/deadlock-api-ingest"
license = "MIT"
edition = "2024"

[dependencies]
serde_json = "1"
anyhow = "1.0.99"
tracing = "0.1.41"
reqwest = { version = "0.12.23", features = ["blocking", "json"] }
regex = "1.11.2"
tracing-subscriber = { version = "0.3.20", features = ["env-filter"] }

[target.'cfg(target_os = "windows")'.dependencies]
pktmon = "0.6.2"

[target.'cfg(target_os = "linux")'.dependencies]
pcap = "2.3.0"
